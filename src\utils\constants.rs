/// GPU Governor 常量定义
/// 将分散的常量集中管理，提高代码可维护性
pub const NOTES: &str = "Mediatek Mali GPU Governor";
pub const AUTHOR: &str = "Author: wa<PERSON><PERSON> @CoolApk, rtools @CoolApk";
pub const SPECIAL: &str =
    "Special Thanks: Ham<PERSON><PERSON> @CoolApk, asto18089 @CoolApk and helloklf @Github";
pub const VERSION: &str = "Version: v2.7";

/// GPU 调频策略常量
pub mod strategy {
    pub const ULTRA_SIMPLE_THRESHOLD: i32 = 99;
    pub const IDLE_THRESHOLD: i32 = 5;
    pub const SAMPLING_INTERVAL_120HZ: u64 = 8; // 8ms = ~120Hz
    pub const FOREGROUND_APP_STARTUP_DELAY: u64 = 60; // seconds
}

// InfoDisplay 结构体已移除，因为不再需要控制台输出
